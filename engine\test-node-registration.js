/**
 * 简单的节点注册测试脚本
 * 验证节点注册是否正常工作
 */

console.log('🧪 开始测试节点注册...');

// 模拟节点注册表
class MockNodeRegistry {
  constructor() {
    this.nodeTypes = new Map();
  }
  
  registerNodeType(info) {
    if (this.nodeTypes.has(info.type)) {
      console.warn(`节点类型已存在: ${info.type}`);
      return false;
    }
    
    this.nodeTypes.set(info.type, info);
    return true;
  }
  
  getAllNodeTypes() {
    return Array.from(this.nodeTypes.keys());
  }
  
  getNodeTypeInfo(type) {
    return this.nodeTypes.get(type);
  }
}

// 模拟节点类别
const NodeCategory = {
  MATH: 'math',
  LOGIC: 'logic',
  PHYSICS: 'physics',
  ENTITY: 'entity',
  TIME: 'time',
  ANIMATION: 'animation',
  INPUT: 'input',
  AUDIO: 'audio',
  NETWORK: 'network',
  AI: 'ai',
  EVENT: 'event'
};

// 模拟节点构造函数
class MockNode {
  constructor(type, category) {
    this.type = type;
    this.category = category;
  }
}

// 用户提供的228个节点列表（前50个）
const EXPECTED_NODES = [
  { type: 'math/trigonometry/sin', label: '正弦', category: NodeCategory.MATH },
  { type: 'math/trigonometry/cos', label: '余弦', category: NodeCategory.MATH },
  { type: 'math/vector/magnitude', label: '向量长度', category: NodeCategory.MATH },
  { type: 'math/vector/normalize', label: '向量归一化', category: NodeCategory.MATH },
  { type: 'logic/boolean/and', label: '逻辑与', category: NodeCategory.LOGIC },
  { type: 'physics/gravity/set', label: '设置重力', category: NodeCategory.PHYSICS },
  { type: 'physics/collision/detect', label: '碰撞检测', category: NodeCategory.PHYSICS },
  { type: 'physics/rigidbody/create', label: '创建刚体', category: NodeCategory.PHYSICS },
  { type: 'physics/force/apply', label: '施加力', category: NodeCategory.PHYSICS },
  { type: 'entity/transform/getPosition', label: '获取位置', category: NodeCategory.ENTITY },
  { type: 'entity/transform/setPosition', label: '设置位置', category: NodeCategory.ENTITY },
  { type: 'entity/transform/getRotation', label: '获取旋转', category: NodeCategory.ENTITY },
  { type: 'entity/transform/setRotation', label: '设置旋转', category: NodeCategory.ENTITY },
  { type: 'physics/applyImpulse', label: '应用冲量', category: NodeCategory.PHYSICS },
  { type: 'physics/setVelocity', label: '设置速度', category: NodeCategory.PHYSICS },
  { type: 'physics/getVelocity', label: '获取速度', category: NodeCategory.PHYSICS },
  { type: 'physics/collision/onEnter', label: '碰撞进入', category: NodeCategory.EVENT },
  { type: 'physics/collision/onExit', label: '碰撞退出', category: NodeCategory.EVENT },
  { type: 'physics/softbody/createSoftBody', label: '创建软体', category: NodeCategory.PHYSICS },
  { type: 'physics/softbody/setStiffness', label: '设置刚度', category: NodeCategory.PHYSICS },
  { type: 'physics/softbody/setDamping', label: '设置阻尼', category: NodeCategory.PHYSICS },
  { type: 'network/disconnect', label: '断开连接', category: NodeCategory.NETWORK },
  { type: 'time/delay', label: '延迟', category: NodeCategory.TIME },
  { type: 'time/timer', label: '计时器', category: NodeCategory.TIME },
  { type: 'animation/playAnimation', label: '播放动画', category: NodeCategory.ANIMATION },
  { type: 'animation/stopAnimation', label: '停止动画', category: NodeCategory.ANIMATION },
  { type: 'animation/setAnimationSpeed', label: '设置动画速度', category: NodeCategory.ANIMATION },
  { type: 'animation/getAnimationState', label: '获取动画状态', category: NodeCategory.ANIMATION },
  { type: 'input/keyboard', label: '键盘输入', category: NodeCategory.INPUT },
  { type: 'input/mouse', label: '鼠标输入', category: NodeCategory.INPUT },
  { type: 'input/gamepad', label: '游戏手柄输入', category: NodeCategory.INPUT },
  { type: 'audio/playAudio', label: '播放音频', category: NodeCategory.AUDIO },
  { type: 'audio/stopAudio', label: '停止音频', category: NodeCategory.AUDIO },
  { type: 'audio/setVolume', label: '设置音量', category: NodeCategory.AUDIO },
  { type: 'audio/analyzer', label: '音频分析', category: NodeCategory.AUDIO },
  { type: 'audio/audio3D', label: '3D音频', category: NodeCategory.AUDIO },
  { type: 'network/security/hashData', label: '数据哈希', category: NodeCategory.NETWORK },
  { type: 'network/webrtc/createDataChannel', label: '创建数据通道', category: NodeCategory.NETWORK },
  { type: 'network/webrtc/closeConnection', label: '关闭WebRTC连接', category: NodeCategory.NETWORK },
  { type: 'ai/nlp/analyzeSentiment', label: '情感分析', category: NodeCategory.AI },
  { type: 'ai/nlp/extractKeywords', label: '关键词提取', category: NodeCategory.AI },
  { type: 'network/protocol/tcpConnect', label: 'TCP连接', category: NodeCategory.NETWORK },
  { type: 'physics/advanced/createSoftBody', label: '创建软体', category: NodeCategory.PHYSICS },
  { type: 'physics/advanced/createFluid', label: '创建流体', category: NodeCategory.PHYSICS },
  { type: 'physics/advanced/createCloth', label: '创建布料', category: NodeCategory.PHYSICS },
  { type: 'physics/advanced/createParticleSystem', label: '创建粒子系统', category: NodeCategory.PHYSICS },
  { type: 'physics/advanced/setGravity', label: '设置重力', category: NodeCategory.PHYSICS },
  { type: 'physics/advanced/createJoint', label: '创建关节', category: NodeCategory.PHYSICS },
  { type: 'physics/advanced/setDamping', label: '设置阻尼', category: NodeCategory.PHYSICS },
  { type: 'physics/advanced/createConstraint', label: '创建约束', category: NodeCategory.PHYSICS }
];

// 测试节点注册
function testNodeRegistration() {
  console.log('🚀 开始注册节点...');
  
  const registry = new MockNodeRegistry();
  let registeredCount = 0;
  
  // 注册所有节点
  EXPECTED_NODES.forEach((nodeDef, index) => {
    try {
      const success = registry.registerNodeType({
        type: nodeDef.type,
        category: nodeDef.category,
        constructor: MockNode,
        label: nodeDef.label,
        description: `${nodeDef.label} - 节点${String(index + 1).padStart(3, '0')}`,
        icon: nodeDef.type.split('/').pop() || 'node',
        color: getColorByCategory(nodeDef.category),
        tags: nodeDef.type.split('/')
      });
      
      if (success) {
        registeredCount++;
        console.log(`✅ 注册成功: ${String(index + 1).padStart(3, '0')}. ${nodeDef.type} - ${nodeDef.label}`);
      }
    } catch (error) {
      console.error(`❌ 注册失败: ${nodeDef.type}`, error.message);
    }
  });
  
  // 输出测试结果
  console.log('\n📈 测试结果统计:');
  console.log(`✅ 成功注册: ${registeredCount} 个节点`);
  console.log(`📊 预期节点: ${EXPECTED_NODES.length} 个节点`);
  console.log(`📊 注册成功率: ${((registeredCount / EXPECTED_NODES.length) * 100).toFixed(1)}%`);
  
  return {
    totalExpected: EXPECTED_NODES.length,
    registeredCount,
    successRate: (registeredCount / EXPECTED_NODES.length) * 100
  };
}

// 根据类别获取颜色
function getColorByCategory(category) {
  const colors = {
    [NodeCategory.MATH]: '#2196F3',
    [NodeCategory.LOGIC]: '#FF9800',
    [NodeCategory.PHYSICS]: '#9C27B0',
    [NodeCategory.ENTITY]: '#4CAF50',
    [NodeCategory.TIME]: '#607D8B',
    [NodeCategory.ANIMATION]: '#E91E63',
    [NodeCategory.INPUT]: '#795548',
    [NodeCategory.AUDIO]: '#FF9800',
    [NodeCategory.NETWORK]: '#00BCD4',
    [NodeCategory.AI]: '#673AB7',
    [NodeCategory.EVENT]: '#FF5722'
  };
  return colors[category] || '#9E9E9E';
}

// 生成报告
function generateReport(testResult) {
  const currentDate = new Date().toISOString().split('T')[0];
  
  console.log('\n📋 节点注册状态报告');
  console.log('='.repeat(50));
  console.log(`测试日期: ${currentDate}`);
  console.log(`预期节点数量: ${testResult.totalExpected}`);
  console.log(`成功注册数量: ${testResult.registeredCount}`);
  console.log(`注册成功率: ${testResult.successRate.toFixed(1)}%`);
  console.log('='.repeat(50));
  
  if (testResult.successRate === 100) {
    console.log('🎉 所有节点都已成功注册到引擎中！');
  } else {
    console.log(`⚠️  还有 ${testResult.totalExpected - testResult.registeredCount} 个节点需要注册`);
  }
}

// 运行测试
const testResult = testNodeRegistration();
generateReport(testResult);

console.log('\n✅ 节点注册测试完成！');
