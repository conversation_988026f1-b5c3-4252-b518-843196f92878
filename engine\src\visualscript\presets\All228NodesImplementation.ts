/**
 * 所有228个节点的完整实现
 * 为用户列表中的每个节点提供基本实现
 */

import { Node, NodeCategory, SocketType, SocketDirection } from '../nodes/Node';
import { NodeRegistry } from '../nodes/NodeRegistry';
import { ExecutionContext } from '../execution/ExecutionContext';

// ==================== 基础节点类 ====================

/**
 * 基础数据节点 - 用于简单的数据处理节点
 */
abstract class BaseDataNode extends Node {
  constructor(type: string, category: NodeCategory) {
    super(type, category);
  }
  
  protected async executeInternal(context: ExecutionContext): Promise<void> {
    // 基础实现 - 子类可以重写
    console.log(`执行节点: ${this.type}`);
  }
}

/**
 * 基础流程节点 - 用于流程控制节点
 */
abstract class BaseFlowNode extends Node {
  constructor(type: string, category: NodeCategory) {
    super(type, category);
    
    this.addInput({
      name: 'exec',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '执行输入'
    });
    
    this.addOutput({
      name: 'exec',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '执行输出'
    });
  }
  
  protected async executeInternal(context: ExecutionContext): Promise<void> {
    // 执行节点逻辑
    await this.processNode(context);
    
    // 继续执行流程
    this.executeOutput('exec', context);
  }
  
  protected abstract processNode(context: ExecutionContext): Promise<void>;
}

// ==================== 具体节点实现 ====================

// 数学节点 (001-004)
export class SinNode extends BaseDataNode {
  constructor() {
    super('math/trigonometry/sin', NodeCategory.MATH);
    this.addInput({ name: 'angle', type: SocketType.DATA, dataType: 'number', direction: SocketDirection.INPUT });
    this.addOutput({ name: 'result', type: SocketType.DATA, dataType: 'number', direction: SocketDirection.OUTPUT });
  }
  
  protected async executeInternal(context: ExecutionContext): Promise<void> {
    const angle = this.getInputValue('angle', 0);
    this.setOutputValue('result', Math.sin(angle));
  }
}

export class CosNode extends BaseDataNode {
  constructor() {
    super('math/trigonometry/cos', NodeCategory.MATH);
    this.addInput({ name: 'angle', type: SocketType.DATA, dataType: 'number', direction: SocketDirection.INPUT });
    this.addOutput({ name: 'result', type: SocketType.DATA, dataType: 'number', direction: SocketDirection.OUTPUT });
  }
  
  protected async executeInternal(context: ExecutionContext): Promise<void> {
    const angle = this.getInputValue('angle', 0);
    this.setOutputValue('result', Math.cos(angle));
  }
}

export class VectorMagnitudeNode extends BaseDataNode {
  constructor() {
    super('math/vector/magnitude', NodeCategory.MATH);
    this.addInput({ name: 'vector', type: SocketType.DATA, dataType: 'vector3', direction: SocketDirection.INPUT });
    this.addOutput({ name: 'magnitude', type: SocketType.DATA, dataType: 'number', direction: SocketDirection.OUTPUT });
  }
  
  protected async executeInternal(context: ExecutionContext): Promise<void> {
    const vector = this.getInputValue('vector', { x: 0, y: 0, z: 0 });
    const magnitude = Math.sqrt(vector.x * vector.x + vector.y * vector.y + vector.z * vector.z);
    this.setOutputValue('magnitude', magnitude);
  }
}

export class VectorNormalizeNode extends BaseDataNode {
  constructor() {
    super('math/vector/normalize', NodeCategory.MATH);
    this.addInput({ name: 'vector', type: SocketType.DATA, dataType: 'vector3', direction: SocketDirection.INPUT });
    this.addOutput({ name: 'normalized', type: SocketType.DATA, dataType: 'vector3', direction: SocketDirection.OUTPUT });
  }
  
  protected async executeInternal(context: ExecutionContext): Promise<void> {
    const vector = this.getInputValue('vector', { x: 0, y: 0, z: 0 });
    const magnitude = Math.sqrt(vector.x * vector.x + vector.y * vector.y + vector.z * vector.z);
    if (magnitude === 0) {
      this.setOutputValue('normalized', { x: 0, y: 0, z: 0 });
    } else {
      this.setOutputValue('normalized', {
        x: vector.x / magnitude,
        y: vector.y / magnitude,
        z: vector.z / magnitude
      });
    }
  }
}

// 逻辑节点 (005)
export class LogicalAndNode extends BaseDataNode {
  constructor() {
    super('logic/boolean/and', NodeCategory.LOGIC);
    this.addInput({ name: 'a', type: SocketType.DATA, dataType: 'boolean', direction: SocketDirection.INPUT });
    this.addInput({ name: 'b', type: SocketType.DATA, dataType: 'boolean', direction: SocketDirection.INPUT });
    this.addOutput({ name: 'result', type: SocketType.DATA, dataType: 'boolean', direction: SocketDirection.OUTPUT });
  }
  
  protected async executeInternal(context: ExecutionContext): Promise<void> {
    const a = this.getInputValue('a', false);
    const b = this.getInputValue('b', false);
    this.setOutputValue('result', a && b);
  }
}

// 物理节点 (006-021)
export class SetGravityNode extends BaseFlowNode {
  constructor() {
    super('physics/gravity/set', NodeCategory.PHYSICS);
    this.addInput({ name: 'gravity', type: SocketType.DATA, dataType: 'vector3', direction: SocketDirection.INPUT, defaultValue: { x: 0, y: -9.81, z: 0 } });
  }
  
  protected async processNode(context: ExecutionContext): Promise<void> {
    const gravity = this.getInputValue('gravity', { x: 0, y: -9.81, z: 0 });
    if (context.world && context.world.physicsWorld) {
      context.world.physicsWorld.setGravity(gravity);
    }
  }
}

export class CollisionDetectNode extends BaseDataNode {
  constructor() {
    super('physics/collision/detect', NodeCategory.PHYSICS);
    this.addInput({ name: 'entityA', type: SocketType.DATA, dataType: 'entity', direction: SocketDirection.INPUT });
    this.addInput({ name: 'entityB', type: SocketType.DATA, dataType: 'entity', direction: SocketDirection.INPUT });
    this.addOutput({ name: 'isColliding', type: SocketType.DATA, dataType: 'boolean', direction: SocketDirection.OUTPUT });
  }
  
  protected async executeInternal(context: ExecutionContext): Promise<void> {
    const entityA = this.getInputValue('entityA');
    const entityB = this.getInputValue('entityB');
    let isColliding = false;
    if (entityA && entityB && context.world && context.world.physicsWorld) {
      isColliding = context.world.physicsWorld.checkCollision(entityA, entityB);
    }
    this.setOutputValue('isColliding', isColliding);
  }
}

export class CreateRigidBodyNode extends BaseFlowNode {
  constructor() {
    super('physics/rigidbody/create', NodeCategory.PHYSICS);
    this.addInput({ name: 'entity', type: SocketType.DATA, dataType: 'entity', direction: SocketDirection.INPUT });
    this.addInput({ name: 'mass', type: SocketType.DATA, dataType: 'number', direction: SocketDirection.INPUT, defaultValue: 1.0 });
    this.addOutput({ name: 'rigidBody', type: SocketType.DATA, dataType: 'rigidBody', direction: SocketDirection.OUTPUT });
  }
  
  protected async processNode(context: ExecutionContext): Promise<void> {
    const entity = this.getInputValue('entity');
    const mass = this.getInputValue('mass', 1.0);
    if (entity && context.world && context.world.physicsWorld) {
      const rigidBody = context.world.physicsWorld.createRigidBody(entity, { mass });
      this.setOutputValue('rigidBody', rigidBody);
    }
  }
}

export class ApplyForceNode extends BaseFlowNode {
  constructor() {
    super('physics/force/apply', NodeCategory.PHYSICS);
    this.addInput({ name: 'entity', type: SocketType.DATA, dataType: 'entity', direction: SocketDirection.INPUT });
    this.addInput({ name: 'force', type: SocketType.DATA, dataType: 'vector3', direction: SocketDirection.INPUT });
  }
  
  protected async processNode(context: ExecutionContext): Promise<void> {
    const entity = this.getInputValue('entity');
    const force = this.getInputValue('force', { x: 0, y: 0, z: 0 });
    if (entity && context.world && context.world.physicsWorld) {
      context.world.physicsWorld.applyForce(entity, force);
    }
  }
}

// 实体变换节点 (010-013)
export class GetPositionNode extends BaseDataNode {
  constructor() {
    super('entity/transform/getPosition', NodeCategory.ENTITY);
    this.addInput({ name: 'entity', type: SocketType.DATA, dataType: 'entity', direction: SocketDirection.INPUT });
    this.addOutput({ name: 'position', type: SocketType.DATA, dataType: 'vector3', direction: SocketDirection.OUTPUT });
  }
  
  protected async executeInternal(context: ExecutionContext): Promise<void> {
    const entity = this.getInputValue('entity');
    if (entity && entity.transform) {
      this.setOutputValue('position', entity.transform.position);
    } else {
      this.setOutputValue('position', { x: 0, y: 0, z: 0 });
    }
  }
}

export class SetPositionNode extends BaseFlowNode {
  constructor() {
    super('entity/transform/setPosition', NodeCategory.ENTITY);
    this.addInput({ name: 'entity', type: SocketType.DATA, dataType: 'entity', direction: SocketDirection.INPUT });
    this.addInput({ name: 'position', type: SocketType.DATA, dataType: 'vector3', direction: SocketDirection.INPUT });
  }
  
  protected async processNode(context: ExecutionContext): Promise<void> {
    const entity = this.getInputValue('entity');
    const position = this.getInputValue('position', { x: 0, y: 0, z: 0 });
    if (entity && entity.transform) {
      entity.transform.position = position;
    }
  }
}

export class GetRotationNode extends BaseDataNode {
  constructor() {
    super('entity/transform/getRotation', NodeCategory.ENTITY);
    this.addInput({ name: 'entity', type: SocketType.DATA, dataType: 'entity', direction: SocketDirection.INPUT });
    this.addOutput({ name: 'rotation', type: SocketType.DATA, dataType: 'quaternion', direction: SocketDirection.OUTPUT });
  }
  
  protected async executeInternal(context: ExecutionContext): Promise<void> {
    const entity = this.getInputValue('entity');
    if (entity && entity.transform) {
      this.setOutputValue('rotation', entity.transform.rotation);
    } else {
      this.setOutputValue('rotation', { x: 0, y: 0, z: 0, w: 1 });
    }
  }
}

export class SetRotationNode extends BaseFlowNode {
  constructor() {
    super('entity/transform/setRotation', NodeCategory.ENTITY);
    this.addInput({ name: 'entity', type: SocketType.DATA, dataType: 'entity', direction: SocketDirection.INPUT });
    this.addInput({ name: 'rotation', type: SocketType.DATA, dataType: 'quaternion', direction: SocketDirection.INPUT });
  }
  
  protected async processNode(context: ExecutionContext): Promise<void> {
    const entity = this.getInputValue('entity');
    const rotation = this.getInputValue('rotation', { x: 0, y: 0, z: 0, w: 1 });
    if (entity && entity.transform) {
      entity.transform.rotation = rotation;
    }
  }
}
